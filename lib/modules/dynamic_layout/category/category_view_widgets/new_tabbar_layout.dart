import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';

import '../../../../common/extensions/buildcontext_ext.dart';
import '../../../../models/entities/category.dart';
import '../../config/category_config.dart';
import '../../index.dart';
import 'clothes_category.dart';

class NewTabBarLayout extends HookWidget {
  final List<Category> categories;

  final CommonItemConfig commonItemConfig;

  const NewTabBarLayout({
    super.key,
    required this.categories,
    required this.commonItemConfig,
  });

  @override
  Widget build(BuildContext context) {
    final tabBarController = useTabController(initialLength: 3);
    final tabIndex = useState(0);

    Widget categoriesWidget() {
      return StaggeredGrid.count(
          crossAxisCount: context.screenWidth > 600 ? 3 : 2,
          mainAxisSpacing: 4.0,
          children: categories.map((category) {
            return ClothesCategoryImageItem(
              cat: category,
              width: MediaQuery.sizeOf(context).width / 2.3,
              height: context.screenHeight / 2.8,
              // itemSize?.height,
              commonConfig: commonItemConfig,
            );
          }).toList());
    }

    Widget tabBarWidget() {
      return TabBar(
        onTap: (index) {
          tabIndex.value = index;
        },
        labelStyle: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontSize: 15,
            ),
        unselectedLabelStyle: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontSize: 15,
            ),
        controller: tabBarController,
        tabs: [
          Tab(text: context.tr.offers),
          Tab(text: context.tr.sales),
          Tab(text: context.tr.factories),
        ],
      );
    }

    Widget tabViewWidget() {
      return IndexedStack(
        index: tabIndex.value,
        children: [
          PinterestLayout(
              config: ProductConfig.empty(), isFeaturedProducts: true),
          PinterestLayout(config: ProductConfig.empty(), isSalesProducts: true),
          categoriesWidget(),
        ],
      );
    }

    return Column(
      children: [
        tabBarWidget(),
        const SizedBox(height: 10),
        tabViewWidget(),
      ],
    );
  }
}
