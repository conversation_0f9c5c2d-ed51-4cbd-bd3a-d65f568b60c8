import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fstore/models/vendor/country_model.dart';
import 'package:fstore/models/main_media_model.dart';
import 'package:fstore/models/vendor/currency_model.dart';

import '../../common/constants.dart';
import 'config_model.dart';

enum VendorType {
  free,
  deleted,
  monthly,
  quarter,
  semester,
  annually,
}

class VendorModel {
  final int? id;
  final String? fcmToken;
  final String? documentId;
  final String? name;
  final String? email;
  final bool? isActive;
  final String? address;
  final String? businessName;
  final String? phone;
  final MainMediaModel? logo;
  final List<CurrencyModel>? currencies;
  final List<CountryModel>? countries;

  final VendorType? vendorType;
  final String? businessType;

  final ConfigModel? config;

  final AboutVendorModel? aboutVendor;

  final String playStoreLink;
  final String appStoreLink;

  final num? globalSale;
  final bool? isGlobalSalePercentage;

  const VendorModel({
    this.id,
    this.logo,
    this.fcmToken,
    this.businessName,
    this.documentId,
    this.vendorType,
    this.countries,
    this.name,
    this.email,
    this.isActive = true,
    this.address,
    this.phone,
    this.currencies,
    this.businessType,
    this.aboutVendor,
    this.config,
    this.playStoreLink = '',
    this.appStoreLink = '',
    this.globalSale,
    this.isGlobalSalePercentage,
  });

  String get logoUrl =>
      'https://storage.googleapis.com/idea2app-dev.appspot.com/Image/Finance_Logo_F_d370246309/Finance_Logo_F_d370246309.png';
  // logo?.url ?? '';

  Color? get getPrimaryColor {
    // Ensure the color string starts with '0x'
    // if (config?.primaryColor != null && config?.primaryColor!.length == 8) {
    return const Color(0xFFb77b1b);
    return Color(int.parse('0x${config!.primaryColor!}'));
    // }

    return null;
  }

  //? Business types
  bool get isClothes =>
      businessType?.toLowerCase() == 'clothes' ||
      businessType?.toLowerCase() == 'others';

  bool get isMarket => businessType?.toLowerCase() == 'market';

  bool get isAccessories => businessType?.toLowerCase() == 'accessories';

  bool get isRestaurant => businessType?.toLowerCase() == 'restaurant';

  bool get isFree => vendorType == VendorType.free;

  static VendorType getVendorType(String type) {
    switch (type) {
      case 'free':
        return VendorType.free;
      case 'deleted':
        return VendorType.deleted;
      case 'monthly':
        return VendorType.monthly;
      case 'quarter':
        return VendorType.quarter;
      case 'semester':
        return VendorType.semester;
      case 'annually':
        return VendorType.annually;
      default:
        return VendorType.free;
    }
  }

  factory VendorModel.fromJson(Map<String, dynamic> json) {
    // final countriesList = json['country'] ?? [];

    printLog('asfafpoafiojwf ${json['logo']}');

    return VendorModel(
      id: json['id'],
      documentId: json['documentId'],
      logo: json['logo'] != null ? MainMediaModel.fromJson(json['logo']) : null,
      name: json['name'] ?? '',
      businessName: json['business_name'] ?? 'default',
      email: json['email'] ?? '',
      isActive: json['active'] ?? true,
      vendorType: getVendorType(json['type'] ?? 'free'),
      address: json['address'] ?? '',
      phone: json['phone'] ?? '',
      fcmToken: json['device_token'],
      businessType:
          json['business_type']?.toString().toLowerCase() ?? 'default',
      // countries: countriesList != null && countriesList is! List<int>
      //     ? List<CountryModel>.from(
      //         countriesList.map(
      //           (x) => CountryModel.fromJson(x),
      //         ),
      //       )
      //     : [],
      currencies: json['currencies'] != null && json['currencies'] is! List<int>
          ? List<CurrencyModel>.from(
              json['currencies'].map(
                (x) => CurrencyModel.fromJson(x),
              ),
            )
          : [],
      aboutVendor: json['about'] != null
          ? AboutVendorModel.fromJson(json['about'])
          : null,
      config:
          json['config'] != null ? ConfigModel.fromJson(json['config']) : null,
      playStoreLink: json['play_store_link'] ?? '',
      appStoreLink: json['app_store_link'] ?? '',
      globalSale: json['global_sale'],
      isGlobalSalePercentage: json['is_global_sale_percentage'] ?? true,

      // countries: countries
    );
  }

  @override
  String toString() {
    return 'VendorModel{id: $id, documentId: $documentId name: $name, email: $email, isActive: $isActive, address: $address, phone: $phone, logo: ${logo?.url}, currencies: $currencies, countries: $countries, vendorType: $vendorType, businessType: $businessType, businessName: $businessName}';
  }
}

// class ConfigModel {
//   final bool showMap;
//   final bool showBoundaries;
//   final bool isActiveWorkingTime;
//   final num minimumOrderCost;
//   final String? primaryColor;
//   final String? defaultLanguage;
//
//   const ConfigModel({
//     this.showMap = false,
//     this.showBoundaries = false,
//     this.isActiveWorkingTime = true,
//     this.minimumOrderCost = 0,
//     this.primaryColor,
//     this.defaultLanguage,
//   });
//
//   // color (if start with ff add 0x)
//   // int? get getPrimaryColor {
//   //   if (primaryColor != null && primaryColor!.length == 8) {
//   //     return int.parse(primaryColor!);
//   //   }
//   //
//   //   return null;
//   // }
//
//   factory ConfigModel.fromJson(Map<String, dynamic> json) {
//     return ConfigModel(
//       showMap: json['show_map'] ?? false,
//       showBoundaries: json['show_boundaries'] ?? false,
//       isActiveWorkingTime: json['is_active_working_time'] ?? true,
//       minimumOrderCost: json['minimum_order_cost'] ?? 0,
//       primaryColor: json['primary_color'],
//       defaultLanguage: json['default_language'],
//     );
//   }
// }

class AboutVendorModel {
  final String privacy;
  final String about;
  final String whatsapp;
  final String facebook;
  final String instagram;
  final String tiktok;

  const AboutVendorModel({
    this.privacy = '',
    this.about = '',
    this.whatsapp = '',
    this.facebook = '',
    this.instagram = '',
    this.tiktok = '',
  });

  factory AboutVendorModel.fromJson(Map<String, dynamic> json) {
    return AboutVendorModel(
      privacy: json['privacy'] ?? '',
      about: json['about'] ?? '',
      whatsapp: json['whatsapp'] ?? '',
      facebook: json['facebook'] ?? '',
      instagram: json['instagram'] ?? '',
      tiktok: json['tiktok'] ?? '',
    );
  }
}
