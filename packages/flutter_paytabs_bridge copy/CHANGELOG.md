## 2.0.3-beta

* First releases

## 2.0.4

* Fixed android issue

## 2.0.5

* logs

## 2.0.6

* Auth Transaction Type

## 2.1.0

* Added alternative payment methods

## 2.1.1

* apply some fixes

## 2.1.2

* integrate iraq end-points

## 2.1.3

* fix handle merchant logo image

## 2.1.4

* add mada network

## 2.1.7

* fixed multiple payments in one session issue
* fixed logo directory issue

## 2.1.8

* fixed apm cancel callback

## 2.1.9

* fixed localization issue on payment screen

## 2.2.0

* fix meeza and add billingLinking option

## 2.2.1

* fix meeza and add billingLinking option on ios and android

## 2.2.2

* change name validation, expiry date is now 2 digits max

## 2.2.3

* change name validation

## 2.2.4

* fix tokenization type

## 2.2.5

* minor fix

## 2.2.6

* minor fix

## 2.2.7

* added transaction states to transaction details

## 2.2.8

* add amman and urpay to apms, fix minor issues

## 2.3.0

* Add support for 3ds
* Add tokenization methods

## 2.3.1

* Add Query function

## 2.3.2

* Fix hide card scan icon issue

## 2.3.3

* sending timeout errors

## 2.3.4

* Fix duplicate transaction error
* Fix some ui issues
* Implement latest native sdk

## 2.3.5

* Fix card scanner issue

## 2.3.6

* add double check to query
* add validation to billing to 3ds payment

## 2.4.0

* Add digital product
* Add trace to error
* Minor fixes

## 2.4.1

* Add new tokenization type to ios

## 2.4.2

* Minor fixes

## 2.4.3

* Add GooglePay, SamsungPay

## 2.4.3

* Add GooglePay, SamsungPay

## 2.4.4

* Remove GooglePay
* revert proguard

## 2.4.5

* Fix cart description validation

## 2.4.6

* Improve card scanner

## 2.5.0

* Improve card scanner

## 2.5.1

* Improve card scanner

## 2.6.0

* Bug Fix

## 2.6.1

* Bug Fix

## 2.6.2

* Add Souhoola APM, cancel payment function and timeout for the card payment screen.

## 2.6.3

* Bug Fix

## 2.6.4

* Add clearSavedCards function.

## 2.6.5

* Bug Fix

## 2.6.6

* Add Tabby APM

## 2.6.7

* Bug Fix

## 2.6.8

* Discounts Feature

## 2.6.9

* Dark Theme colors in iOS